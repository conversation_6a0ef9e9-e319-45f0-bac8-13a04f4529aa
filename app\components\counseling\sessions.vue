                  <text class="info-label">来访者</text>
                  <text class="info-value">{{ session.client_name || '匿名' }}</text>
                </view>
              </view>
              <view class="info-col">
                <view class="info-icon chart"></view>
                <view class="info-content">
                  <text class="info-label">性别/年龄</text>
                  <text class="info-value">{{ getGenderText(session.client_gender) }}/{{ session.client_age || '未知' }}</text>
                </view>
              </view>
            </view>

            <view class="session-info-row">
              <view class="info-col">
                <view class="info-icon time"></view>
                <view class="info-content">
                  <text class="info-label">咨询时间</text>
                  <text class="info-value time-value">{{ formatDate(session.session_date || session.scheduled_time) }}</text>
                </view>
              </view>
              <view class="info-col">
                <!-- 修复咨询时长字段的图标显示 -->
                <image class="info-icon" src="/static/icons/time-start.svg" mode="aspectFit"></image>
                <view class="info-content">
                  <text class="info-label">咨询时长</text>
                  <text class="info-value">{{ getSessionDuration(session) }}</text>
                </view>
              </view>
            </view>

            <view class="session-info-row" v-if="session.counselor || (session.analysis && session.analysis.risk_level)">
              <view class="info-col" v-if="session.counselor">
                <view class="info-icon therapist"></view>
                <view class="info-content">
                  <text class="info-label">咨询师</text>
                  <text class="info-value">{{ session.counselor.name || '未分配' }}</text>
                </view>
              </view>
              <view class="info-col" v-if="session.analysis && session.analysis.risk_level">
                <view class="info-icon risk"></view>
                <view class="info-content">
                  <text class="info-label">风险等级</text>
                  <text class="info-value risk-level" :class="getRiskLevelClass(session.analysis.risk_level)">
                    {{ getRiskLevelText(session.analysis.risk_level) }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 描述和分析状态 -->
          <view class="session-footer" v-if="(session.description && session.description !== session.topic) || session.analysis">
            <view class="session-description" v-if="session.description && session.description !== session.topic">
              <text class="description-text">{{ session.description }}</text>
            </view>
            <!-- 显示分析状态（如果有） -->
            <view class="analysis-status" v-if="session.analysis && session.analysis.status">
              <text class="analysis-label">分析:</text>
              <text class="analysis-value" :class="getAnalysisStatusClass(session.analysis.status)">
                {{ getAnalysisStatusText(session.analysis.status) }}
              </text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="session-actions">
            <view class="action-buttons">
              <button
                v-if="canViewReport(session)"
                class="action-button report-button"
                @click.stop="viewReport(session.id)"
              >
                查看报告
              </button>
              <button
                v-if="canViewReport(session)"
                class="action-button share-button"
                @click.stop="shareReport(session.id)"
              >
                分享报告
              </button>
              <button
                v-else-if="canAnalyze(session)"
                class="action-button analyze-button"
                @click.stop="startAnalysis(session.id)"
              >
                开始分析
              </button>

              <button
                v-if="canCancelSession(session)"
                class="action-button cancel-button"
                @click.stop="confirmCancelSession(session.id)"
              >
                取消咨询
              </button>
              <button
                v-if="canRateSession(session)"
                class="action-button rate-button"
                @click.stop="rateSession(session.id)"
              >
                评价
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 悬浮按钮 -->
    <view v-if="showFloatingButton" class="floating-button" @click="startNewSession">
      <text class="floating-button-icon">+</text>
    </view>
  </view>
</template>

<script>
import counselingService from '../../services/counselingService';

export default {
  name: 'CounselingSessionsComponent',
  props: {
    // 是否显示悬浮按钮
    showFloatingButton: {
      type: Boolean,
      default: true
    },
    // 角色过滤
    role: {
      type: String,
      default: 'all'
    },
    // 是否自动加载数据
    autoLoad: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      sessions: [],
      loading: true
    }
  },
  created() {
    if (this.autoLoad) {
      this.loadSessions()
    }
  },
  methods: {
    async loadSessions() {
      try {
        this.loading = true

        console.log(`加载咨询会话列表: role=${this.role}`)

        // 调用咨询服务
        const result = await counselingService.getSessions(this.role)

        if (result.success) {
          console.log('获取咨询会话列表成功:', result.data)
          this.sessions = result.data || []

          // 调试：检查会话数据结构
          if (this.sessions.length > 0) {
            console.log('第一个会话数据结构:', JSON.stringify(this.sessions[0], null, 2))
            console.log('会话数量:', this.sessions.length)
            console.log('有analysis对象的会话数量:', this.sessions.filter(s => s.analysis).length)

            // 检查每个会话的analysis对象
            this.sessions.forEach((session, index) => {
              if (session.analysis) {
                console.log(`会话 ${index} (ID: ${session.id}) 的analysis对象:`,
                  JSON.stringify(session.analysis, null, 2))
              }
            })

            // 检查每个会话是否可以查看报告
            this.sessions.forEach((session, index) => {
              const canView = this.canViewReport(session)
              console.log(`会话 ${index} (ID: ${session.id}) 是否可以查看报告: ${canView}`)
            })
          }

          // 检查分析状态和客户信息
          this.sessions.forEach(session => {
            // 记录分析状态
            if (session.analysis) {
              console.log(`会话 ID: ${session.id}, 分析状态: ${session.analysis.status}`)

              // 确保风险等级存在
              if (session.analysis.status === 'completed' && !session.analysis.risk_level) {
                console.log(`会话 ID: ${session.id} 分析已完成但没有风险等级，设置默认风险等级: low`)
                session.analysis.risk_level = 'low'
              }
            }

            // 检查客户信息是否完整
            if (!session.client_name || !session.client_gender || !session.client_age) {
              console.warn(`会话 ID: ${session.id} 的客户信息不完整`)
            }
          })

          // 为已完成的会话获取分析状态
