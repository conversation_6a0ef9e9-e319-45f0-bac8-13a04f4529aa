<template>
  <view class="session-detail-page">
    <view class="content-container">
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>

      <view v-else-if="!session" class="empty-state">
        <text class="empty-icon">search</text> <!-- Placeholder for actual icon -->
        <text class="empty-title">未找到会话</text>
        <text class="empty-description">该咨询会话不存在或已被删除</text>
        <button class="action-button button-primary" @click="goBack">返回列表</button>
      </view>

      <view v-else class="session-detail">
        <!-- 会话信息卡片（合并了标题和会话信息） -->
        <view class="detail-card info-card">
          <!-- 标题部分 -->
          <view class="card-header">
            <view class="title-container">
              <text class="card-title">{{ session.title || '咨询会话' }}</text>
              <view class="id-status-container">
                <text class="session-id">会话ID: {{ formatSessionId(session.session_id || sessionId) }}</text>
                <text class="status-badge" :class="getStatusClass(session.status)">{{ getStatusText(session.status) }}</text>
                <view v-if="session.analysis" class="analysis-status-container">
                  <text class="status-badge" :class="getAnalysisStatusClass(session.analysis.status)">
                    {{ getAnalysisStatusText(session.analysis.status) }}
                  </text>
                  <button v-if="session.analysis.status === 'processing'" class="refresh-button" @click="loadSessionDetail">
                    刷新
                  </button>
                </view>
              </view>
            </view>
          </view>

          <view class="card-divider"></view>

          <!-- 会话信息部分 -->
          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">咨询时间</text>
              <text class="info-value">{{ formatDate(session.session_date || session.scheduled_time) }}</text>
            </view>

            <view class="info-item">
              <text class="info-label">咨询时长</text>
              <text class="info-value">{{ getSessionDuration(session) }}</text>
            </view>

            <view class="info-item" v-if="session.counselor">
              <text class="info-label">咨询师</text>
              <text class="info-value">{{ session.counselor.name || '未分配' }}</text>
            </view>

            <view class="info-item" v-if="session.client_name">
              <text class="info-label">来访者</text>
              <text class="info-value">{{ session.client_name || '匿名' }}</text>
            </view>

            <view class="info-item" v-if="session.client_gender">
              <text class="info-label">性别</text>
              <text class="info-value">{{ getGenderText(session.client_gender) }}</text>
            </view>

            <view class="info-item" v-if="session.client_age">
              <text class="info-label">年龄</text>
              <text class="info-value">{{ session.client_age || '未知' }}</text>
            </view>

            <view class="info-item" v-if="session.analysis && session.analysis.risk_level">
              <text class="info-label">风险等级</text>
              <text class="status-badge risk-level" :class="getRiskLevelClass(session.analysis.risk_level)">
                {{ getRiskLevelText(session.analysis.risk_level) }}
              </text>
            </view>
          </view>

          <!-- 咨询描述 -->
          <view class="info-description" v-if="session.description">
            <text class="description-label">咨询描述</text>
            <text class="description-text">{{ session.description }}</text>
          </view>

          <!-- 备注 -->
          <view class="info-description" v-if="session.notes">
            <text class="description-label">备注</text>
            <text class="notes-text">{{ session.notes }}</text>
          </view>
        </view>



        <!-- 咨询报告生成中提示 -->
        <view v-if="session.analysis && session.analysis.status === 'processing'" class="report-processing-alert">
          <view class="alert-icon-container">
            <text class="alert-icon">⏳</text> <!-- 沙漏图标 -->
            <view class="processing-indicator"></view> <!-- 旋转动画指示器 -->
          </view>
          <view class="alert-content">
            <text class="alert-title">咨询报告生成中</text>
            <text class="alert-description">系统正在分析对话内容并生成报告，这可能需要几分钟时间。请稍候，报告生成后将自动通知您。</text>
            <view class="refresh-container">
              <button class="refresh-button alert-refresh" @click="loadSessionDetail">
                刷新状态
              </button>
            </view>
          </view>
        </view>

        <!-- 操作按钮卡片 -->
        <view class="detail-card action-card">
          <view class="card-section-title">
            <text>操作选项</text>
          </view>

          <view class="action-buttons">
            <button
              v-if="canRecordSession(session)"
              class="action-button button-primary"
              @click="startSession"
            >
              <text>开始录制</text>
            </button>

            <button
              v-if="canViewReport(session)"
              class="action-button button-primary"
              @click="viewReport"
            >
              <text>查看报告</text>
            </button>

            <button
              v-else-if="canAnalyze(session)"
              class="action-button button-primary"
              @click="startAnalysis"
            >
              <text>开始分析</text>
            </button>

            <button
              v-if="session && session.recordings && Array.isArray(session.recordings) && session.recordings.length > 0"
              class="action-button button-secondary"
              :class="{
                'button-disabled': session.analysis && session.analysis.status === 'processing'
              }"
              @click="(!session.analysis || session.analysis.status !== 'processing') ? reanalyze() : null"
              :disabled="session.analysis && session.analysis.status === 'processing'"
            >
              <text>{{ (session.analysis && session.analysis.status === 'processing') ? '分析中...' : '重新分析' }}</text>
            </button>

            <button
              v-if="canCancelSession(session)"
              class="action-button button-destructive"
              @click="confirmCancelSession"
            >
              <text>取消咨询</text>
            </button>

            <button
              v-if="canRateSession(session)"
              class="action-button button-secondary-amber"
              @click="showRatingModal"
            >
              <text>评价咨询</text>
            </button>
          </view>
        </view>

      </view>
    </view>

    <!-- 评价弹窗 -->
    <view class="rating-modal" v-if="showRating">
      <view class="rating-modal-mask" @click="hideRatingModal"></view>
      <view class="rating-modal-content">
        <view class="rating-modal-header">
          <text class="rating-modal-title">评价咨询</text>
          <button class="rating-modal-close" @click="hideRatingModal">
            <text class="iconfont icon-close"></text>
          </button>
        </view>

        <view class="rating-form">
          <view class="rating-item">
            <text class="rating-label">整体评分</text>
            <view class="star-rating">
              <view
                v-for="i in 5"
                :key="i"
                class="star"
                :class="{ 'star-active': i <= rating.score }"
                @click="setRating(i)"
              >
                <text class="iconfont" :class="i <= rating.score ? 'icon-star-filled' : 'icon-star'"></text>
              </view>
            </view>
          </view>

          <view class="rating-item">
            <text class="rating-label">评价内容</text>
            <textarea
              class="rating-textarea"
              v-model="rating.comment"
              placeholder="请分享您对本次咨询的感受和建议..."
              maxlength="500"
            />
            <text class="textarea-counter">{{ rating.comment.length }}/500</text>
          </view>

          <view class="rating-item switch-item">
            <text class="rating-label">是否匿名</text>
            <switch
              :checked="rating.anonymous"
              @change="handleAnonymousChange"
              color="#2563EB"
            />
          </view>

          <button
            class="action-button button-primary submit-rating-button"
            :disabled="!isRatingValid"
            :class="{ 'button-disabled': !isRatingValid }"
            @click="submitRating"
          >
            提交评价
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import counselingService from '../../services/counselingService';

export default {
  data() {
    return {
      sessionId: null,
      session: null,
      loading: true,
      showRating: false,
      rating: {
        score: 0,
        comment: '',
        anonymous: false
      },
      submitting: false,
      // 轮询相关属性
      pollingTimer: null, // 轮询定时器
      pollingInterval: 10000, // 轮询间隔（毫秒）
      pollingEnabled: true, // 是否启用轮询
      lastPollingTime: 0, // 上次轮询时间
      previousAnalysisStatus: null, // 用于UI逻辑
      analysisStartTime: 0, // 分析开始时间
      analysisJustInitiatedByRecordingPage: false,
    }
  },
  computed: {
    isRatingValid() {
      return this.rating.score > 0;
    }
  },
  onLoad(options) {
    if (options.id) {
      this.sessionId = options.id;

      // 先加载会话详情
      this.loadSessionDetail();

      // 然后加载录音记录
      this.loadSessionRecordings();

      // 不在这里启动轮询，而是在loadSessionDetail和loadSessionRecordings中根据状态决定是否启动
      // 这样可以避免不必要的轮询

      if (options.action) {
        this.handleAction(options.action);
      }

      uni.$on('recording-cancelled', this.handleRecordingCancelled);
    } else {
      uni.showToast({
        title: '缺少会话ID',
        icon: 'none'
      });
      setTimeout(() => {
        this.goBack();
      }, 1500);
    }
  },

  onShow() {
    // 检查当前分析状态，如果已经是user_cancelled，则不处理分析启动标志
    const currentAnalysisStatus = this.session?.analysis?.status;
    if (currentAnalysisStatus === 'user_cancelled') {
      console.log('当前状态为user_cancelled，跳过分析启动标志处理');
      // 清除可能存在的标志
      uni.removeStorageSync('analysis_initiation_flag');
    } else {
      const flag = uni.getStorageSync('analysis_initiation_flag');
      if (flag && flag.sessionId === this.sessionId) {
        const fiveMinutes = 5 * 60 * 1000;
        if ((Date.now() - flag.timestamp) < fiveMinutes) {
          console.log('Analysis initiation flag found for session:', this.sessionId);
          this.analysisJustInitiatedByRecordingPage = true;
        } else {
          console.log('Analysis initiation flag found but is too old for session:', this.sessionId);
        }
        uni.removeStorageSync('analysis_initiation_flag');
      } else {
        // Optional: log if a flag was found but for a different session or malformed
        if (flag) {
          console.log('Mismatched or malformed analysis initiation flag found:', flag);
          uni.removeStorageSync('analysis_initiation_flag'); // Clean up mismatched flag
        }
      }
    }

    // 页面显示时也可能需要重新加载或检查状态，特别是从其他页面返回时
    if (this.sessionId) {
      this.loadSessionDetail();
      this.loadSessionRecordings();
      // uni.$on('recording-cancelled', this.handleRecordingCancelled); // Removed as it's already in onLoad
    }
  },

  onUnload() {
    // 停止轮询
    this.stopPolling();
    uni.$off('recording-cancelled', this.handleRecordingCancelled);
  },
  methods: {
    handleRecordingCancelled(data) {
      if (data && data.sessionId === this.sessionId) {
        console.log(`Received recording-cancelled event for session: ${this.sessionId}`);

        // 设置分析状态为用户取消
        if (this.session && this.session.analysis) {
          this.session.analysis.status = 'user_cancelled';
        } else if (this.session) {
          this.session.analysis = { status: 'user_cancelled' };
        }

        // 停止轮询
        this.stopPolling();

        // 清除分析启动标志，防止onShow时误判
        uni.removeStorageSync('analysis_initiation_flag');

        // 重置分析刚启动的标志
        this.analysisJustInitiatedByRecordingPage = false;

        // 强制更新UI
        this.$forceUpdate();

        // 显示取消提示
        uni.showToast({
          title: '已取消',
          icon: 'none',
          duration: 2000
        });

        console.log(`录音已取消，分析状态设置为: user_cancelled`);
      }
    },

    async loadSessionDetail() {
      try {
        this.loading = true;
        const result = await counselingService.getSession(this.sessionId);
        if (result.success && result.data) {
          this.session = { status: 'new', ...result.data };
          if (!this.session.status) {
            this.session.status = 'new';
          }

          // 如果没有分析数据，设置为未开始状态
          if (!this.session.analysis) {
            this.session.analysis = { status: null };
          }

          // 保存当前分析状态，用于后续比较
          const currentAnalysisStatus = this.session.analysis.status;

          // 如果当前状态是user_cancelled，保护这个状态不被覆盖
          if (currentAnalysisStatus === 'user_cancelled') {
            console.log('检测到user_cancelled状态，保护此状态不被覆盖');
            this.stopPolling(); // 确保停止轮询
            return; // 直接返回，不进行后续的状态检查和修改
          }

          // 检查是否有录音文件
          const hasRecordings = this.session?.recordings && Array.isArray(this.session.recordings) && this.session.recordings.length > 0;

          // 如果没有录音文件，且当前状态不是completed、failed或user_cancelled，则设置为未开始状态
          const shouldResetToNotStarted = !hasRecordings &&
                                       currentAnalysisStatus !== 'completed' &&
                                       currentAnalysisStatus !== 'failed' &&
                                       currentAnalysisStatus !== 'user_cancelled';
          if (shouldResetToNotStarted) {
            console.log(`没有录音文件且状态适合重置，将状态从 ${currentAnalysisStatus} 设置为 null`);
            this.session.analysis.status = null;
            this.stopPolling();
          } else if (!hasRecordings) { // Added this else-if for clarity
            console.log(`没有录音文件，但状态为 ${currentAnalysisStatus} (completed, failed, or user_cancelled)，保持不变`);
          }
          // 有录音文件且状态为处理中或等待分析，启动轮询
          else if (this.session?.analysis?.status === 'processing' || this.session?.analysis?.status === 'pending') {
            if (!this.pollingTimer) {
              this.startPolling();
            }
          }
          // 如果分析已完成或失败，停止轮询
          else if (this.session?.analysis?.status === 'completed' || this.session?.analysis?.status === 'failed') {
            this.stopPolling();
          }
        } else {
          uni.showToast({ title: '获取会话详情失败', icon: 'none' });
        }
      } catch (error) {
        console.error('Error loading session detail:', error);
        uni.showToast({ title: '加载会话详情失败', icon: 'none' });
      } finally {
        this.loading = false;
      }
    },

    async loadSessionRecordings() {
      try {
        if (!this.sessionId) return [];

        // 发起HTTP请求获取录音列表
        const result = await counselingService.getSessionRecordings(this.sessionId);
        if (result.success && result.recordings) {
          if (this.session) {
            // 保存之前的状态，用于比较
            const prevStatus = this.session?.analysis?.status;

            // 更新录音列表
            this.session.recordings = result.recordings;

            // 记录录音数量
            console.log(`会话录音数量: ${result.recordings.length}`);

            // 如果没有录音，且之前的状态不是completed、failed或user_cancelled，则设置为未开始状态
            if (result.recordings.length === 0) {
              if (!this.session.analysis) this.session.analysis = {};

              const canResetStatus = prevStatus !== 'completed' &&
                               prevStatus !== 'failed' &&
                               prevStatus !== 'user_cancelled';
              if (canResetStatus) {
                console.log(`没有录音文件且状态适合重置，将状态从 ${prevStatus} 设置为 null`);
                this.session.analysis.status = null;
                this.stopPolling();
              } else {
                console.log(`没有录音文件，但状态为 ${prevStatus} (completed, failed, or user_cancelled)，保持不变`);
              }
            } else {
              // 有录音文件，获取最新分析状态
              console.log(`检测到 ${result.recordings.length} 个录音文件，获取最新分析状态`);
              await this.getAnalysisStatus(); // 获取最新分析状态
            }
          }
          return result.recordings;
        }

        // 如果没有录音或请求失败，设置为空数组，但不强制修改状态
        if (this.session) {
          const prevStatus = this.session?.analysis?.status;
          this.session.recordings = [];
          const canResetStatusOnFail = prevStatus !== 'completed' &&
                                     prevStatus !== 'failed' &&
                                     prevStatus !== 'user_cancelled';
          if (canResetStatusOnFail) {
            if (!this.session.analysis) this.session.analysis = {};
            console.log(`获取录音失败且状态适合重置，将状态从 ${prevStatus} 设置为 null`);
            this.session.analysis.status = null;
            this.stopPolling();
          } else {
            console.log(`获取录音失败，但状态为 ${prevStatus} (completed, failed, or user_cancelled)，保持不变`);
          }
        }
        return [];
      } catch (error) {
        console.error('Error loading session recordings:', error);
        // 出错时也设置为空数组，但不强制修改状态
        if (this.session) {
          const prevStatus = this.session?.analysis?.status;
          this.session.recordings = [];

          const canResetStatusOnError = prevStatus !== 'completed' &&
                                      prevStatus !== 'failed' &&
                                      prevStatus !== 'user_cancelled';
          if (canResetStatusOnError) {
            if (!this.session.analysis) this.session.analysis = {};
            console.log(`加载录音出错且状态适合重置，将状态从 ${prevStatus} 设置为 null`);
            this.session.analysis.status = null;
            this.stopPolling();
          } else {
            console.log(`加载录音出错，但状态为 ${prevStatus} (completed, failed, or user_cancelled)，保持不变`);
          }
        }
        return [];
      }
    },

    async getAnalysisStatus() {
      try {
        if (!this.sessionId) return;

        // 检查是否有录音文件
        const hasRecordings = this.session?.recordings && Array.isArray(this.session.recordings) && this.session.recordings.length > 0;

        // 如果没有录音文件，检查当前状态
        if (!hasRecordings) {
          const prevStatus = this.session?.analysis?.status;

          // 只有当前状态不是completed、failed或user_cancelled时才修改状态
          const canResetStatus = prevStatus !== 'completed' &&
                               prevStatus !== 'failed' &&
                               prevStatus !== 'user_cancelled';
          if (canResetStatus) {
            if (!this.session.analysis) this.session.analysis = {};
            console.log(`getAnalysisStatus: 没有录音文件且状态适合重置，将状态从 ${prevStatus} 设置为 null`);
            this.session.analysis.status = null;
            this.stopPolling(); // 停止轮询
          } else {
            console.log(`getAnalysisStatus: 没有录音文件，但状态为 ${prevStatus} (completed, failed, or user_cancelled)，保持不变`);
          }
          return;
        }

        // 发起HTTP请求获取分析状态
        const result = await counselingService.getSessionAnalysis(this.sessionId);
        if (result && !result.error) {
          if (!this.session.analysis) this.session.analysis = {};

          this.previousAnalysisStatus = this.session.analysis.status; // 存储之前的状态用于UI逻辑

          // 记录状态变化
          const oldStatus = this.session.analysis.status;
          const newStatus = result.status;

          // 如果有录音文件且状态为pending，强制设置为processing
          if (hasRecordings && newStatus === 'pending') {
            console.log('检测到录音文件存在，将pending状态强制改为processing');
            result.status = 'processing';
          }

          if (result.status) this.session.analysis.status = result.status;
          if (result.risk_level) this.session.analysis.risk_level = result.risk_level;
          if (result.report_content) this.session.analysis.report_content = result.report_content;
          if (result.analysis_results) {
            this.session.analysis.analysis_results = result.analysis_results;
            if (!this.session.analysis.risk_level) {
              try {
                if (result.analysis_results.mental_state?.risk_assessment?.overall_risk) {
                  this.session.analysis.risk_level = result.analysis_results.mental_state.risk_assessment.overall_risk.toLowerCase();
                } else if (result.analysis_results.risk_assessment?.overall_risk) {
                  this.session.analysis.risk_level = result.analysis_results.risk_assessment.overall_risk.toLowerCase();
                } else if (result.analysis_results.preliminary_analysis?.risk_assessment?.overall_risk) {
                  this.session.analysis.risk_level = result.analysis_results.preliminary_analysis.risk_assessment.overall_risk.toLowerCase();
                }
              } catch (e) {
                if (!this.session.analysis.risk_level) this.session.analysis.risk_level = 'low';
              }
            }
          }
          if (this.session.analysis.status === 'completed' && !this.session.analysis.risk_level) {
            this.session.analysis.risk_level = 'low';
          }

          // 检测状态变化，特别是从processing到completed的变化
          if (oldStatus !== newStatus) {
            console.log(`分析状态变化: ${oldStatus} -> ${newStatus}`);

            // 如果状态变为completed或failed，处理完成逻辑
            // 但只有在之前的状态不是completed或failed时才处理
            if ((newStatus === 'completed' || newStatus === 'failed') &&
                (oldStatus !== 'completed' && oldStatus !== 'failed')) {
              this.handleAnalysisCompletion(newStatus);
            }
          }

          this.$forceUpdate(); // 确保UI更新

          // 根据分析状态管理轮询
          if (this.session?.analysis?.status === 'processing' || this.session?.analysis?.status === 'pending') {
            // 如果分析正在进行中，确保轮询已启动
            if (!this.pollingTimer) {
              this.startPolling();
            }
          } else {
            // 如果分析已完成或失败，停止轮询
            this.stopPolling();
          }
        }
      } catch (error) {
        console.error('Error getting analysis status:', error);
      }
    },

    // 处理分析完成
    handleAnalysisCompletion(status) {
      // 检查是否已经处理过这个状态，避免重复处理
      if (this.previousAnalysisStatus === status) {
        console.log(`状态已经是 ${status}，跳过处理`);
        return;
      }

      // 检查是否是页面初始加载时的状态更新
      const isInitialLoad = !this.lastPollingTime;

      // 只有在非初始加载时才显示提示
      if (!isInitialLoad) {
        // 显示提示
        uni.showToast({
          title: status === 'completed' ? '分析已完成' : '分析失败',
          icon: status === 'completed' ? 'success' : 'none',
          duration: 3000
        });
      } else {
        console.log(`初始加载时检测到状态为 ${status}，不显示提示`);
      }

      // 停止轮询
      this.stopPolling();

      // 直接更新状态，避免重新加载
      if (!this.session.analysis) this.session.analysis = {};
      this.session.analysis.status = status;
      this.$forceUpdate();

      // 记录已处理的状态
      this.previousAnalysisStatus = status;
    },

    // 启动轮询
    startPolling() {
      // 清理现有的轮询定时器
      this.stopPolling();

      // 检查是否有录音文件
      const hasRecordings = this.session?.recordings && Array.isArray(this.session.recordings) && this.session.recordings.length > 0;

      // 检查当前分析状态
      const currentStatus = this.session?.analysis?.status;
      const isTerminalStatus = currentStatus === 'completed' || currentStatus === 'failed' || currentStatus === 'user_cancelled';

      // 如果没有录音文件，但状态已经是 terminal (completed, failed, user_cancelled)，不修改状态
      if (!hasRecordings) {
        if (isTerminalStatus) {
          console.log(`没有录音文件，但状态为 ${currentStatus} (terminal)，不修改状态，不启动轮询`);
          return;
        }
        // If no recordings and not a terminal status, it might have been set to not_started by previous logic.
        // We only start polling if there are recordings OR if status is already processing/pending.
        console.log('没有录音文件，且状态非terminal，不启动轮询 (除非状态已是processing/pending)');
        if (currentStatus !== 'processing' && currentStatus !== 'pending') {
           return;
        }
      }

      // 如果状态已经是terminal (completed, failed, user_cancelled)，不启动轮询
      if (isTerminalStatus) {
        console.log(`分析状态为 ${currentStatus} (terminal)，不启动轮询`);
        return;
      }

      // 记录分析开始时间（如果尚未记录）
      if (!this.analysisStartTime && (this.session?.analysis?.status === 'processing' || this.session?.analysis?.status === 'pending')) {
        this.analysisStartTime = Date.now();
        console.log(`记录分析开始时间: ${new Date(this.analysisStartTime).toLocaleTimeString()}`);
      }

      // 设置新的轮询定时器
      if (this.pollingEnabled) {
        console.log(`启动轮询，间隔: ${this.pollingInterval}ms`);
        this.pollingTimer = setInterval(() => {
          // 再次检查是否有录音文件
          const stillHasRecordings = this.session?.recordings && Array.isArray(this.session.recordings) && this.session.recordings.length > 0;
          const currentStatus = this.session?.analysis?.status;
          const isTerminalStatus = currentStatus === 'completed' || currentStatus === 'failed' || currentStatus === 'user_cancelled';

          // 如果没有录音文件，但状态已经是 terminal (completed, failed, user_cancelled)，不停止轮询 (it shouldn't have started)
          // This part of logic might be redundant if startPolling itself prevents starting in these states without recordings.
          // However, if it somehow started, and recordings disappeared, and status is terminal, we should stop.
          if (!stillHasRecordings) {
            if (isTerminalStatus) {
              console.log(`轮询中：没有录音文件，但状态为 ${currentStatus} (terminal)，停止轮询`);
              this.stopPolling();
              return;
            } else {
              console.log('轮询中：没有录音文件，且状态非terminal，停止轮询');
              this.stopPolling();
              return;
            }
          }

          // 检查是否需要轮询（只有在分析中状态才需要）
          const analysisStatus = this.session?.analysis?.status;
          if (analysisStatus !== 'processing' && analysisStatus !== 'pending') {
            console.log(`分析状态为 ${analysisStatus}，停止轮询`);
            this.stopPolling();
            return;
          }

          // 记录当前时间
          this.lastPollingTime = Date.now();

          // 加载最新状态
          console.log('轮询: 获取最新分析状态');
          this.getAnalysisStatus();
        }, this.pollingInterval);

        // 设置最大轮询时间，5分钟后自动停止轮询
        if (this.analysisStartTime) {
          const maxPollingTime = 5 * 60 * 1000; // 5分钟
          const timeElapsed = Date.now() - this.analysisStartTime;

          if (timeElapsed > maxPollingTime) {
            console.log(`分析时间已超过${maxPollingTime/60000}分钟，停止轮询`);
            this.stopPolling();

            // 最后再检查一次状态
            this.getAnalysisStatus();
            return;
          }
        }
      }
    },

    // 停止轮询
    stopPolling() {
      if (this.pollingTimer) {
        console.log('停止轮询');
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }

      // 如果状态已经是terminal (completed, failed, user_cancelled)，重置分析开始时间
      if (this.session?.analysis?.status === 'completed' || this.session?.analysis?.status === 'failed' || this.session?.analysis?.status === 'user_cancelled') {
        this.analysisStartTime = 0;
      }
    },

    // 处理会话分析状态更新
    handleAnalysisStatusUpdate(data) {
      if (!this.session.analysis) this.session.analysis = {};

      this.previousAnalysisStatus = this.session.analysis.status;

      // 更新分析状态
      if (data.status) this.session.analysis.status = data.status;
      if (data.risk_level) this.session.analysis.risk_level = data.risk_level;
      if (data.report_content) this.session.analysis.report_content = data.report_content;
      if (data.analysis_results) this.session.analysis.analysis_results = data.analysis_results;

      // 确保分析完成时有风险等级
      if (this.session.analysis.status === 'completed' && !this.session.analysis.risk_level) {
        this.session.analysis.risk_level = 'low';
      }

      // 分析完成或失败时的处理
      if (data.status === 'completed' || data.status === 'failed') {
        // 检查是否是页面初始加载时的状态更新
        const isInitialLoad = !this.lastPollingTime;

        // 只有在非初始加载时才显示提示
        if (!isInitialLoad) {
          uni.showToast({
            title: data.status === 'completed' ? '分析已完成' : '分析失败',
            icon: data.status === 'completed' ? 'success' : 'none',
            duration: 3000
          });
        } else {
          console.log(`初始加载时检测到状态为 ${data.status}，不显示提示`);
        }

        // 停止轮询
        this.stopPolling();

        // 确保状态更新后，重新加载详情以获取最终状态
        setTimeout(() => {
          this.loadSessionDetail();
          this.loadSessionRecordings();

          // 再次延迟检查，确保状态已正确更新
          setTimeout(() => {
            // 如果状态仍然不正确，强制更新
            if (this.session?.analysis?.status !== data.status) {
              if (!this.session.analysis) this.session.analysis = {};
              this.session.analysis.status = data.status;
              this.$forceUpdate();
            }
          }, 1000);
        }, 500);
      }
    },

    // 处理录音状态更新
    handleRecordingsUpdate(data) {
      if (data.recordings && Array.isArray(data.recordings)) {
        this.session.recordings = data.recordings;
        console.log('Updated recordings from WebSocket:', data.recordings.length);
      }
    },

    // 处理旧版消息格式（兼容性）
    handleLegacyUpdate(data) {
      if (!this.session.analysis) this.session.analysis = {};

      this.previousAnalysisStatus = this.session.analysis.status;

      // 更新分析状态
      if (data.status) this.session.analysis.status = data.status;
      if (data.risk_level) this.session.analysis.risk_level = data.risk_level;
      if (data.report_content) this.session.analysis.report_content = data.report_content;
      if (data.analysis_results) this.session.analysis.analysis_results = data.analysis_results;

      // 处理录音状态更新
      if (data.recordings) {
        this.session.recordings = data.recordings;
      }

      // 确保分析完成时有风险等级
      if (this.session.analysis.status === 'completed' && !this.session.analysis.risk_level) {
        this.session.analysis.risk_level = 'low';
      }

      // 分析完成或失败时的处理
      if (data.status === 'completed' || data.status === 'failed') {
        // 检查是否是页面初始加载时的状态更新
        const isInitialLoad = !this.lastPollingTime;

        // 只有在非初始加载时才显示提示
        if (!isInitialLoad) {
          uni.showToast({
            title: data.status === 'completed' ? '分析已完成' : '分析失败',
            icon: data.status === 'completed' ? 'success' : 'none',
            duration: 3000
          });
        } else {
          console.log(`初始加载时检测到状态为 ${data.status}，不显示提示`);
        }

        // 停止轮询
        this.stopPolling();

        // 确保状态更新后，重新加载详情以获取最终状态
        setTimeout(() => {
          this.loadSessionDetail();
          this.loadSessionRecordings();

          // 再次延迟检查，确保状态已正确更新
          setTimeout(() => {
            // 如果状态仍然不正确，强制更新
            if (this.session?.analysis?.status !== data.status) {
              if (!this.session.analysis) this.session.analysis = {};
              this.session.analysis.status = data.status;
              this.$forceUpdate();
            }
          }, 1000);
        }, 500);
      }
    },

    formatDate(date) {
      if (!date) return '未设置';
      const d = new Date(date);
      return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')} ${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}`;
    },

    getStatusText(status) {
      const map = { 'scheduled': '已预约', 'in_progress': '进行中', 'completed': '已完成', 'cancelled': '已取消' };
      return map[status] || '';
    },
    getStatusClass(status) {
      const map = { 'scheduled': 'status-scheduled', 'in_progress': 'status-in-progress', 'completed': 'status-completed', 'cancelled': 'status-cancelled' };
      return map[status] || '';
    },
    getGenderText(gender) {
      if (!gender) return '未知';
      const map = { 'male': '男', 'female': '女', 'other': '其他' };
      return map[gender.toLowerCase()] || gender;
    },
    getRiskLevelText(level) {
      if (!level) return '未评估';
      const map = { 'low': '低风险', 'medium': '中等风险', 'high': '高风险', 'critical': '严重风险' };
      return map[level.toLowerCase()] || level;
    },
    getRiskLevelClass(level) {
      if (!level) return '';
      const map = { 'low': 'risk-low', 'medium': 'risk-medium', 'high': 'risk-high', 'critical': 'risk-critical' };
      return map[level.toLowerCase()] || '';
    },
    getAnalysisStatusText(status) {
      if (!status) return '未开始';
      const map = { 'not_started': '未开始', 'pending': '等待分析', 'processing': '分析中', 'completed': '分析完成', 'failed': '分析失败', 'user_cancelled': '已取消' };
      if (!map[status]) {
        if (status.includes('fail') || status.includes('error')) return '分析失败';
        if (status.includes('process') || status.includes('running')) return '分析中';
        if (status.includes('complete') || status.includes('done') || status.includes('success')) return '分析完成';
        if (status.includes('wait') || status.includes('pending')) return '等待分析';
        return '未开始';
      }
      return map[status];
    },
    getAnalysisStatusClass(status) {
      if (!status) return 'status-not-started';
      const map = {
        'not_started': 'status-not-started',
        'pending': 'status-pending',
        'processing': 'status-processing',
        'completed': 'status-completed analysis-completed',
        'failed': 'status-failed',
        'user_cancelled': 'status-cancelled'
      };
      if (!map[status]) {
        if (status.includes('fail') || status.includes('error')) return 'status-failed';
        if (status.includes('process') || status.includes('running')) return 'status-processing';
        if (status.includes('complete') || status.includes('done') || status.includes('success')) return 'status-completed analysis-completed';
        if (status.includes('wait') || status.includes('pending')) return 'status-pending';
        return 'status-not-started';
      }
      return map[status];
    },
    getSessionDuration(session) {
      if (session.recordings?.length > 0 && session.recordings[0].duration_seconds) {
        const secs = session.recordings[0].duration_seconds;
        return `${Math.floor(secs / 60)}:${(secs % 60).toString().padStart(2, '0')} (实际时长)`;
      }
      return session.duration_minutes ? `${session.duration_minutes} 分钟` : '未设置';
    },
    formatSessionId(id) {
      if (!id) return '';
      if (typeof id === 'string' && id.startsWith('cs_')) return parseInt(id.substring(3, 11), 16).toString().padStart(8, '0');
      if (typeof id === 'string' && id.includes('-')) return id.split('-')[0];
      if (typeof id === 'number' || !isNaN(parseInt(id))) return parseInt(id).toString().padStart(8, '0');
      return id.toString();
    },
    canViewReport(session) { return session?.analysis?.status === 'completed' && !!session?.analysis?.report_content; },
    canAnalyze(session) { return session.status === 'completed' && (!session.analysis || ['failed', 'pending'].includes(session.analysis.status)); },
    canJoinSession(session) { return session.status !== 'cancelled'; },
    canCancelSession(session) { return session.status === 'scheduled'; },
    canRateSession(session) { return session.status === 'completed'; },
    canRecordSession(session) { return !session.status || ['in_progress', 'scheduled', 'new'].includes(session.status); },
    viewReport() {
      console.log(`跳转到报告页面: sessionId=${this.sessionId}`);
      uni.navigateTo({ url: `/pages/counseling/report?id=${this.sessionId}` });
    },
    async startAnalysis() {
      uni.showLoading({ title: '开始分析...' });
      try {
        const result = await counselingService.analyzeDialogue(this.sessionId);
        if (result.success) {
          uni.showToast({ title: '分析已开始', icon: 'success' });
          if (!this.session.analysis) this.session.analysis = {};
          this.session.analysis.status = 'processing';

          // 启动轮询
          this.startPolling();

          // 记录分析开始时间
          this.analysisStartTime = Date.now();

          // 10秒后刷新一次，确保状态更新
          setTimeout(() => {
            console.log('分析: 10秒后刷新');
            this.loadSessionDetail();
            this.loadSessionRecordings();
          }, 10000);
        } else uni.showToast({ title: result.error || '开始分析失败', icon: 'none' });
      } catch (e) { uni.showToast({ title: '开始分析失败', icon: 'none' }); }
      finally { uni.hideLoading(); }
    },
    async reanalyze() {
      uni.showModal({
        title: '重新分析确认', content: '重新分析将覆盖之前的报告结果。确定要继续吗？',
        confirmText: '继续分析', cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '开始分析...' });
            try {
              console.log(`开始重新分析会话: sessionId=${this.sessionId}`);
              const result = await counselingService.analyzeDialogue(this.sessionId, {}, true, false);
              console.log(`重新分析API响应:`, result);

              if (result && result.success) {
                uni.showToast({ title: '分析已开始', icon: 'success' });
                if (!this.session.analysis) this.session.analysis = {};
                this.session.analysis.status = 'processing';

                // 启动轮询
                this.startPolling();

                // 重置分析开始时间
                this.analysisStartTime = Date.now();
                console.log(`重新分析开始时间: ${new Date(this.analysisStartTime).toLocaleTimeString()}`);

                // 简化刷新逻辑，只在关键时间点刷新
                // 10秒后刷新一次
                setTimeout(() => {
                  console.log('重新分析: 10秒后刷新');
                  this.loadSessionDetail();
                  this.loadSessionRecordings();
                }, 10000);

                // 30秒后再次刷新，确保获取最终状态
                setTimeout(() => {
                  console.log('重新分析: 30秒后刷新');
                  this.loadSessionDetail();
                  this.loadSessionRecordings();

                  // 如果状态仍然是processing，设置一个额外检查
                  if (this.session?.analysis?.status === 'processing' || this.session?.analysis?.status === 'pending') {
                    // 60秒后最后检查一次
                    setTimeout(() => {
                      console.log('重新分析: 60秒后最终检查');

                      // 直接调用API获取最新状态
                      counselingService.getSessionAnalysis(this.sessionId).then(result => {
                        if (result && !result.error) {
                          // 检查状态是否变为completed
                          if (result.status === 'completed' || result.status === 'failed') {
                            console.log(`重新分析: 检测到状态变为 ${result.status}`);

                            // 更新状态
                            if (!this.session.analysis) this.session.analysis = {};
                            this.session.analysis.status = result.status;
                            if (result.risk_level) this.session.analysis.risk_level = result.risk_level;
                            if (result.report_content) this.session.analysis.report_content = result.report_content;
                            if (result.analysis_results) this.session.analysis.analysis_results = result.analysis_results;

                            // 强制更新UI
                            this.$forceUpdate();

                            // 处理完成逻辑
                            this.handleAnalysisCompletion(result.status);
                          } else {
                            // 如果仍在处理中，刷新页面
                            this.loadSessionDetail();
                          }
                        }
                      }).catch(err => {
                        console.error('重新分析: 获取状态出错', err);
                      });
                    }, 30000); // 从30秒后再等30秒，总共60秒
                  }
                }, 30000);
              } else {
                console.error(`重新分析失败，API返回:`, result);
                uni.showToast({ title: result?.error || '开始分析失败', icon: 'none' });
              }
            } catch (e) {
              console.error(`重新分析过程中发生异常:`, e);
              // 检查是否是网络错误
              if (e.errMsg && e.errMsg.includes('request:fail')) {
                uni.showToast({ title: '网络请求失败，请检查网络连接', icon: 'none' });
              } else {
                uni.showToast({ title: '重新分析失败: ' + (e.message || e.errMsg || '未知错误'), icon: 'none' });
              }
            }
            finally { uni.hideLoading(); }
          }
        }
      });
    },
    joinSession() { this.startSession(); },
    confirmCancelSession() {
      uni.showModal({
        title: '取消咨询', content: '确定要取消这个咨询吗？',
        confirmText: '确定', cancelText: '取消',
        success: (res) => { if (res.confirm) this.cancelSession(); }
      });
    },
    async updateSessionStatus(status) {
      uni.showLoading({ title: '更新中...' });
      try {
        const result = await counselingService.updateSession(this.sessionId, { status });
        if (result.success) {
          this.session.status = status;
          const map = { 'in_progress': '咨询已开始', 'completed': '咨询已完成', 'cancelled': '咨询已取消' };
          uni.showToast({ title: map[status] || '状态已更新', icon: 'success' });
          return true;
        }
        uni.showModal({ title: '更新失败', content: result.error || '更新状态失败', showCancel: false });
        return false;
      } catch (e) {
        uni.showModal({ title: '更新失败', content: '更新状态失败', showCancel: false });
        return false;
      } finally { uni.hideLoading(); }
    },
    async cancelSession() {
      if (await this.updateSessionStatus('cancelled')) setTimeout(() => this.goBack(true), 1500);
    },
    showRatingModal() { this.showRating = true; },
    hideRatingModal() { this.showRating = false; },
    setRating(score) { this.rating.score = score; },
    handleAnonymousChange(e) { this.rating.anonymous = e.detail.value; },
    async submitRating() {
      if (!this.isRatingValid || this.submitting) return;
      this.submitting = true;
      uni.showLoading({ title: '提交中...' });
      try {
        const result = await counselingService.submitRating(this.sessionId, this.rating);
        if (result.success) {
          this.hideRatingModal();
          uni.showToast({ title: '评价成功', icon: 'success' });
          setTimeout(() => this.loadSessionDetail(), 1500);
        } else uni.showModal({ title: '评价失败', content: result.error || '提交评价失败', showCancel: false });
      } catch (e) { uni.showModal({ title: '评价失败', content: '提交评价失败', showCancel: false });}
      finally { this.submitting = false; uni.hideLoading(); }
    },
    handleAction(action) {
      setTimeout(() => {
        if (action === 'join' && this.session && this.canJoinSession(this.session)) this.startSession();
        else if (action === 'rate' && this.session && this.canRateSession(this.session)) this.showRatingModal();
      }, 500);
    },
    async startSession() {
      uni.showModal({
        title: '准备开始录音', content: '系统将开始录制咨询对话，并自动识别咨询师和来访者角色。确认开始录音吗？',
        confirmText: '开始录音', cancelText: '取消',
        success: async (res) => { if (res.confirm) await this.checkRecordingsAndProceed(); }
      });
    },
    async checkRecordingsAndProceed() {
      uni.showLoading({ title: '检查录音状态...', mask: true });
      let currentRecordings = [];
      try { currentRecordings = await this.loadSessionRecordings(); }
      catch (e) { currentRecordings = this.session?.recordings || []; }
      finally { uni.hideLoading(); }

      if (currentRecordings.length > 0) {
        uni.showModal({
          title: '确认操作', content: '检测到已有录音文件。继续将删除之前的录音并开始新的录制，是否继续？',
          confirmText: '继续录制', cancelText: '取消',
          success: async (res) => {
            if (res.confirm) {
              uni.showLoading({ title: '正在删除旧录音...' });
              try {
                const delRes = await counselingService.deleteSessionRecordings(this.sessionId);
                if (delRes.success) {
                  if (this.session) this.session.recordings = [];
                  this.proceedToRecording();
                } else uni.showToast({ title: `删除旧录音失败: ${delRes.error || ''}`, icon: 'none', duration: 3000 });
              } catch (e) { uni.showToast({ title: '删除旧录音时出错', icon: 'none', duration: 3000 }); }
              finally { uni.hideLoading(); }
            }
          }
        });
      } else this.proceedToRecording();
    },
    proceedToRecording() {
      if (this.session && ['scheduled', 'new'].includes(this.session.status)) {
        this.updateSessionStatus('in_progress');
      }
      uni.navigateTo({ url: `/pages/counseling/recording?sessionId=${this.sessionId}` });
    },
    handleRecordingComplete() { this.loadSessionDetail(); },
    handleTranscriptionComplete() { uni.showToast({ title: '转录完成', icon: 'success' }); },
    handleAnalysisComplete() { this.loadSessionDetail(); },
    goBack(refresh = false) {
      if (refresh) {
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];
        if (prevPage?.vm?.loadSessions) prevPage.vm.loadSessions();
      }
      uni.navigateBack();
    }
  }
}
</script>

<style>
/* General Page & Typography */
.session-detail-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F9FAFB; /* Gray-50 */
  color: #374151; /* Gray-700 */
  font-family: 'Inter', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
}

.content-container {
  flex: 1;
  padding: 20px;
}

/* Base Card Styling */
.detail-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
  padding: 20px;
  margin-bottom: 20px;
}

/* Loading & Empty States */
.loading-container,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #DBEAFE; /* Blue-100 */
  border-top-color: #2563EB; /* Blue-600 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #9CA3AF; /* Gray-400 */
}

.empty-title,
.card-title,
.alert-title,
.rating-modal-title {
  font-size: 18px;
  font-weight: 600; /* Semibold */
  color: #111827; /* Gray-900 */
  margin-bottom: 8px;
}
.card-title { margin-bottom: 6px; }
.alert-title { margin-bottom: 4px; font-size: 15px; }
.rating-modal-title { margin-bottom: 0; }


.empty-description {
  font-size: 14px;
  color: #6B7280; /* Gray-500 */
  margin-bottom: 24px;
  line-height: 1.6;
  max-width: 300px;
}

/* Title Card - 已合并到info-card */

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.card-divider {
  height: 1px;
  background-color: #E5E7EB; /* Gray-200 */
  margin: 0 -20px 20px -20px; /* 负边距延伸到卡片边缘 */
}

.title-container {
  flex: 1;
}

.id-status-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.session-id {
  font-size: 12px;
  color: #6B7280; /* Gray-500 */
  font-family: 'Menlo', 'Monaco', Consolas, "Liberation Mono", "Courier New", monospace;
}

/* Status Badges (General) */
.status-badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.2;
}

/* Specific Status Badge Colors */
.status-scheduled, .status-processing { background-color: #DBEAFE; color: #1E40AF; } /* Blue */
.status-in-progress, .status-completed.analysis-completed { background-color: #D1FAE5; color: #047857; } /* Green */
.status-completed { background-color: #E5E7EB; color: #374151; } /* Gray for general completed */
.status-pending { background-color: #E5E7EB; color: #4B5563; } /* Gray */
.status-not-started { background-color: #F3F4F6; color: #6B7280; } /* Light Gray */
.status-cancelled, .status-failed { background-color: #FEE2E2; color: #991B1B; } /* Red */
.risk-low { background-color: #D1FAE5; color: #047857; } /* Green */
.risk-medium { background-color: #FEF3C7; color: #B45309; } /* Amber */
.risk-high { background-color: #FEE2E2; color: #B91C1C; } /* Red */
.risk-critical { background-color: #FFE4E6; color: #B91C1C; } /* Lighter Red/Pink */


.analysis-status-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-button {
  color: #2563EB; /* Blue-600 */
  font-size: 12px;
  cursor: pointer;
  font-weight: 500;
  background: #EFF6FF; /* Blue-50 */
  border: none;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 4px;
  height: 20px;
  line-height: 20px;
  display: flex;
  align-items: center;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
}
.refresh-button:hover {
  color: #1D4ED8; /* Blue-700 */
  background-color: #DBEAFE; /* Blue-100 */
}

.card-section-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500; /* Medium */
  color: #374151; /* Gray-700 */
}

/* Session Info Card */
.info-card {
  /* 合并后的卡片样式 */
  padding-bottom: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-label {
  font-size: 12px;
  color: #6B7280; /* Gray-500 */
  font-weight: 400; /* Normal */
}

.info-value {
  font-size: 14px;
  color: #1F2937; /* Gray-800 */
  font-weight: 500; /* Medium */
}

.info-description {
  margin-top: 16px;
  padding: 12px 16px;
  background-color: #F9FAFB; /* Gray-50 */
  border-radius: 8px;
  border: 1px solid #E5E7EB; /* Gray-200 */
}

.description-label {
  font-size: 13px;
  font-weight: 500; /* Medium */
  color: #374151; /* Gray-700 */
  margin-bottom: 6px;
  display: block;
}

.description-text,
.notes-text {
  font-size: 14px;
  color: #4B5563; /* Gray-600 */
  line-height: 1.6;
  white-space: pre-wrap;
}
.notes-text {
  font-style: italic;
  color: #6B7280; /* Gray-500 */
}

/* Report Processing Alert */
.report-processing-alert {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #FFFBEB; /* Amber-50 */
  border: 1px solid #FDE68A; /* Amber-300 */
  border-left-width: 4px;
  border-left-color: #F59E0B; /* Amber-500 */
  border-radius: 8px;
  margin-bottom: 20px;
}

.alert-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-icon {
  font-size: 20px;
  color: #F59E0B; /* Amber-500 */
  z-index: 2;
}

.processing-indicator {
  position: absolute;
  width: 28px;
  height: 28px;
  border: 2px solid rgba(245, 158, 11, 0.2); /* Amber-500 with opacity */
  border-top-color: #F59E0B; /* Amber-500 */
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
  z-index: 1;
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* .alert-title is defined with .empty-title */
.alert-description {
  font-size: 13px;
  color: #D97706; /* Amber-600 */
  line-height: 1.5;
  margin-bottom: 8px;
}

.refresh-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.alert-refresh {
  font-size: 12px;
  padding: 2px 8px;
  background-color: #FFFBEB; /* Amber-50 */
  border: 1px solid #FDE68A; /* Amber-200 */
  color: #B45309; /* Amber-700 */
}

/* Action Buttons */

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: flex-start;
}

.action-button {
  flex-grow: 1;
  flex-basis: calc(50% - 6px); /* Approx 2 buttons per row */
  min-height: 40px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500; /* Medium */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.15s ease-in-out;
  border: 1px solid transparent;
  line-height: 1.5;
  cursor: pointer;
}
.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.07);
}
.action-button:active {
  transform: translateY(0px);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.button-primary { background-color: #2563EB; color: #FFFFFF; border-color: #2563EB; } /* Blue-600 */
.button-primary:hover { background-color: #1D4ED8; border-color: #1D4ED8; } /* Blue-700 */

.button-secondary { background-color: #FFFFFF; color: #2563EB; border-color: #D1D5DB; } /* Gray-300 border */
.button-secondary:hover { background-color: #F9FAFB; border-color: #9CA3AF; color: #1D4ED8; } /* Gray-50 bg, Gray-400 border */

.button-secondary-amber { background-color: #FFFFFF; color: #B45309; border-color: #FDE68A; } /* Amber-300 border */
.button-secondary-amber:hover { background-color: #FFFBEB; border-color: #FCD34D; color: #92400E; } /* Amber-50 bg, Amber-400 border */

.button-destructive { background-color: #FFFFFF; color: #DC2626; border-color: #FECACA; } /* Red-300 border */
.button-destructive:hover { background-color: #FEF2F2; border-color: #F87171; color: #B91C1C; } /* Red-50 bg, Red-400 border */

.button-disabled,
.action-button[disabled] {
  background-color: #F3F4F6 !important; /* Gray-100 */
  color: #9CA3AF !important; /* Gray-400 */
  border-color: #E5E7EB !important; /* Gray-200 */
  opacity: 1 !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  transform: none !important;
}

/* Rating Modal */
.rating-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(17, 24, 39, 0.6); /* Gray-900 with opacity */
  padding: 16px;
}
.rating-modal-mask {
  position: absolute; top: 0; left: 0; right: 0; bottom: 0;
}
.rating-modal-content {
  position: relative;
  width: 100%;
  max-width: 500px;
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
  padding: 24px;
  overflow-y: auto;
  max-height: 90vh;
}

.rating-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
/* .rating-modal-title defined with .empty-title */

.rating-modal-close {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}
.rating-modal-close:hover { background-color: #F3F4F6; /* Gray-100 */ }
.rating-modal-close .iconfont { font-size: 20px; color: #6B7280; display: block; /* Gray-500 */ }

.rating-form { display: flex; flex-direction: column; gap: 20px; }
.rating-item { display: flex; flex-direction: column; }
.rating-item.switch-item { flex-direction: row; justify-content: space-between; align-items: center; }

.rating-label { font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500; } /* Gray-700 */
.switch-item .rating-label { margin-bottom: 0; }

.star-rating { display: flex; gap: 8px; }
.star { cursor: pointer; }
.star .iconfont { font-size: 28px; color: #D1D5DB; transition: color 0.15s ease-in-out; } /* Gray-300 */
.star:hover .iconfont { color: #FBBF24; } /* Amber-400 */
.star.star-active .iconfont { color: #F59E0B; } /* Amber-500 */

.rating-textarea {
  width: 100%;
  min-height: 100px;
  padding: 10px 12px;
  border: 1px solid #D1D5DB; /* Gray-300 */
  border-radius: 8px;
  background-color: #FFFFFF;
  font-size: 14px;
  color: #1F2937; /* Gray-800 */
  line-height: 1.5;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.rating-textarea::placeholder { color: #9CA3AF; /* Gray-400 */ }
.rating-textarea:focus {
  border-color: #2563EB; /* Blue-600 */
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
  outline: none;
}
.textarea-counter { display: block; text-align: right; font-size: 12px; color: #6B7280; margin-top: 6px; } /* Gray-500 */

/* UniApp Switch color prop should be used in template: :color="'#2563EB'" */
.rating-item switch[checked], /* Fallback for web if needed */
uni-switch[checked] .uni-switch-input::after { /* More specific for UniApp web */
  background-color: #2563EB !important;
  border-color: #2563EB !important;
}
uni-switch[checked] .uni-switch-input { /* Target the track */
    border-color: #2563EB !important;
    background-color: #2563EB !important;
}

.submit-rating-button {
  margin-top: 16px;
  /* Uses .action-button and .button-primary classes from template */
}

/* Iconfont */
.iconfont { font-family: 'iconfont'; } /* Ensure font-face is declared elsewhere */
.icon-close::before { content: '\e664'; } /* Example, ensure this matches your font */
.icon-star::before { content: '\e7df'; }
.icon-star-filled::before { content: '\e7e0'; }
</style>